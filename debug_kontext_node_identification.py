#!/usr/bin/env python3
"""
调试Kontext工作流节点识别问题
检查双图工作流中的节点配置和识别逻辑
"""

import json
import os
from typing import Dict, Any, Optional

def load_workflow_file(workflow_file: str) -> Optional[Dict[str, Any]]:
    """加载工作流文件"""
    try:
        file_path = os.path.join('workflows', workflow_file)
        if not os.path.exists(file_path):
            print(f"❌ 工作流文件不存在: {file_path}")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载工作流文件失败: {e}")
        return None

def analyze_workflow_nodes(workflow_data: Dict[str, Any], workflow_name: str):
    """分析工作流节点"""
    print(f"\n🔍 分析工作流: {workflow_name}")
    print("=" * 60)
    
    # 统计节点类型
    node_types = {}
    image_input_nodes = []
    image_output_nodes = []
    vaedecode_nodes = []
    preview_nodes = []
    
    for node_id, node_data in workflow_data.items():
        class_type = node_data.get('class_type', '')
        title = node_data.get('_meta', {}).get('title', '')
        
        # 统计节点类型
        if class_type not in node_types:
            node_types[class_type] = 0
        node_types[class_type] += 1
        
        # 分类节点
        if 'image_input' in title.lower():
            image_input_nodes.append((node_id, title, class_type))
        
        if class_type == 'VAEDecode':
            vaedecode_nodes.append((node_id, title, class_type))
        
        if class_type == 'PreviewImage':
            preview_nodes.append((node_id, title, class_type))
        
        # 可能的图片输出节点（有images输出的节点）
        if class_type in ['VAEDecode', 'PreviewImage', 'easy loadImageBase64']:
            image_output_nodes.append((node_id, title, class_type))
    
    print(f"📊 节点总数: {len(workflow_data)}")
    print(f"📊 节点类型统计:")
    for class_type, count in sorted(node_types.items()):
        print(f"   {class_type}: {count}")
    
    print(f"\n🖼️ 图片输入节点:")
    for node_id, title, class_type in image_input_nodes:
        print(f"   节点{node_id}: {title} ({class_type})")
    
    print(f"\n🎯 VAEDecode节点:")
    for node_id, title, class_type in vaedecode_nodes:
        print(f"   节点{node_id}: {title} ({class_type})")
        if title == 'final_image_output':
            print(f"      ✅ 这是final_image_output节点！")
    
    print(f"\n👁️ PreviewImage节点:")
    for node_id, title, class_type in preview_nodes:
        print(f"   节点{node_id}: {title} ({class_type})")
    
    print(f"\n📤 可能的图片输出节点:")
    for node_id, title, class_type in image_output_nodes:
        print(f"   节点{node_id}: {title} ({class_type})")

def simulate_strict_identification(workflow_data: Dict[str, Any], workflow_name: str):
    """模拟严格节点识别逻辑"""
    print(f"\n🔬 模拟严格识别逻辑: {workflow_name}")
    print("-" * 40)
    
    # 模拟有图片输出的节点（实际运行时这些节点会有images输出）
    potential_output_nodes = []
    for node_id, node_data in workflow_data.items():
        class_type = node_data.get('class_type', '')
        if class_type in ['VAEDecode', 'PreviewImage', 'easy loadImageBase64']:
            potential_output_nodes.append(node_id)
    
    print(f"🔍 潜在的图片输出节点: {potential_output_nodes}")
    
    # 严格识别逻辑
    for node_id in potential_output_nodes:
        if node_id in workflow_data:
            node_data = workflow_data[node_id]
            title = node_data.get('_meta', {}).get('title', '')
            class_type = node_data.get('class_type', '')
            
            print(f"🔍 检查节点{node_id}: title='{title}', class_type='{class_type}'")
            
            if title == 'final_image_output' and class_type == 'VAEDecode':
                print(f"✅ 找到final_image_output节点: {node_id}")
                return node_id
    
    print(f"❌ 未找到title='final_image_output'且class_type='VAEDecode'的节点")
    return None

def main():
    """主函数"""
    print("🔍 Kontext工作流节点识别调试工具")
    print("=" * 60)
    
    # 检查的工作流文件
    workflows = [
        ("kontext_local_1image.json", "单图工作流"),
        ("kontext_local_2images.json", "双图工作流"),
        ("kontext_local_3images.json", "三图工作流"),
        ("kontext_api_1image.json", "API单图工作流"),
        ("kontext_api_2images.json", "API双图工作流"),
        ("kontext_api_3images.json", "API三图工作流"),
    ]
    
    for workflow_file, workflow_name in workflows:
        workflow_data = load_workflow_file(workflow_file)
        if workflow_data:
            analyze_workflow_nodes(workflow_data, workflow_name)
            final_node = simulate_strict_identification(workflow_data, workflow_name)
            
            if final_node:
                print(f"🎯 最终选择的节点: {final_node}")
            else:
                print(f"⚠️ 严格模式下未找到有效节点")
        else:
            print(f"❌ 无法加载工作流: {workflow_file}")
    
    print(f"\n📋 总结:")
    print(f"- 如果双图工作流返回第一张上传图片，可能的原因:")
    print(f"  1. 节点191 (image_input_01) 被误选为输出节点")
    print(f"  2. 严格识别逻辑没有正确执行")
    print(f"  3. 工作流执行过程中节点8没有产生输出")
    print(f"  4. API工作流被误用（PreviewImage类型的final_image_output）")

if __name__ == "__main__":
    main()
