"""
[二次开发文件] Kontext 会话管理模块
负责用户会话的创建、获取、生命周期管理等

修复说明：
- 将 KontextSessionManager 改为单例模式，解决多实例导致的会话数据不同步问题
- 确保所有地方使用同一个会话管理器实例
"""
from typing import Dict, Optional
import time

class KontextSession:
    def __init__(self, user_id: str, chat_id: str, data: Optional[dict] = None, expire_seconds: int = 3600):
        self.user_id = user_id
        self.chat_id = chat_id
        self.data = data or {}
        self.created_at = time.time()
        self.expire_seconds = expire_seconds

    def is_expired(self) -> bool:
        return (time.time() - self.created_at) > self.expire_seconds

class KontextSessionManager:
    """
    管理Kontext用户会话 - 单例模式

    修复说明：
    - 使用单例模式确保全局只有一个实例
    - 解决多实例导致的会话数据不同步问题
    """
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # 防止重复初始化
        if self._initialized:
            return

        self.sessions: Dict[str, KontextSession] = {}
        self._initialized = True

        # 添加调试信息
        import logging
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🔧 KontextSessionManager 单例实例已创建，实例ID: {id(self)}")

    def _session_key(self, user_id: str, chat_id: str) -> str:
        return f"{user_id}::{chat_id}"

    def get_user_session(self, user_id: str, chat_id: str) -> Optional[KontextSession]:
        key = self._session_key(user_id, chat_id)
        session = self.sessions.get(key)

        self.logger.debug(f"🔍 [KONTEXT-SESSION] 获取会话 - key: {key}, 实例ID: {id(self)}, 会话总数: {len(self.sessions)}")

        if session and not session.is_expired():
            self.logger.debug(f"✅ [KONTEXT-SESSION] 找到有效会话 - key: {key}")
            return session
        elif session and session.is_expired():
            self.logger.debug(f"⏰ [KONTEXT-SESSION] 会话已过期，自动清理 - key: {key}")
            del self.sessions[key]
        else:
            self.logger.debug(f"❌ [KONTEXT-SESSION] 未找到会话 - key: {key}")

        return None

    def create_session(self, user_id: str, chat_id: str, data: Optional[dict] = None, expire_seconds: int = 3600) -> KontextSession:
        key = self._session_key(user_id, chat_id)

        # 如果已存在会话，先清理
        if key in self.sessions:
            self.logger.info(f"🔄 [KONTEXT-SESSION] 替换现有会话 - key: {key}")
            del self.sessions[key]

        session = KontextSession(user_id, chat_id, data, expire_seconds)
        self.sessions[key] = session

        self.logger.info(f"✨ [KONTEXT-SESSION] 创建新会话 - key: {key}, 实例ID: {id(self)}, 会话总数: {len(self.sessions)}")
        return session

    def remove_session(self, user_id: str, chat_id: str) -> bool:
        key = self._session_key(user_id, chat_id)

        if key in self.sessions:
            del self.sessions[key]
            self.logger.info(f"🗑️ [KONTEXT-SESSION] 移除会话成功 - key: {key}, 实例ID: {id(self)}, 剩余会话: {len(self.sessions)}")
            return True
        else:
            self.logger.warning(f"⚠️ [KONTEXT-SESSION] 尝试移除不存在的会话 - key: {key}, 实例ID: {id(self)}")
            return False

    def cleanup_expired_sessions(self) -> int:
        expired_keys = [k for k, s in self.sessions.items() if s.is_expired()]

        for k in expired_keys:
            del self.sessions[k]

        if expired_keys:
            self.logger.info(f"🧹 [KONTEXT-SESSION] 清理过期会话 - 清理数量: {len(expired_keys)}, 剩余会话: {len(self.sessions)}")

        return len(expired_keys)

    def get_session_count(self) -> int:
        """获取当前会话数量"""
        return len(self.sessions)

    def get_all_sessions_info(self) -> Dict[str, Dict]:
        """获取所有会话信息（用于调试）"""
        return {
            key: {
                "user_id": session.user_id,
                "chat_id": session.chat_id,
                "created_at": session.created_at,
                "is_expired": session.is_expired(),
                "data_keys": list(session.data.keys()) if session.data else []
            }
            for key, session in self.sessions.items()
        }

# 🔥 修复完成：
# 1. 改为单例模式，解决多实例问题
# 2. 添加详细的调试日志
# 3. 改进会话清理逻辑
# 4. 添加会话统计方法